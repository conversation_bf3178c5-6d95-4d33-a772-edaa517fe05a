// TypeScript interfaces for the MKOLANI website

export interface Slide {
  image: string;
  title: string;
  subtitle: string;
}

export interface Program {
  title: string;
  duration: string;
  qualifications: string;
  fees: string;
}

export interface GalleryImage {
  src: string;
  alt: string;
  category: string;
}

export interface NavigationProps {
  isMenuOpen: boolean;
  setIsMenuOpen: (isOpen: boolean) => void;
  scrollToSection: (sectionId: string) => void;
}

export interface HeroProps {
  slides: Slide[];
  currentSlide: number;
  setCurrentSlide: (index: number) => void;
  scrollToSection: (sectionId: string) => void;
}

export interface AnnouncementsProps {
  announcements: string[];
  currentAnnouncement: number;
}

export interface ProgramsProps {
  programs: Program[];
}

export interface GalleryProps {
  images: GalleryImage[];
}

export interface FooterProps {
  scrollToSection: (sectionId: string) => void;
}

export interface ContactInfo {
  address: string;
  phone: string;
  email: string;
}

export interface SocialLink {
  name: string;
  url: string;
  icon: string;
}
