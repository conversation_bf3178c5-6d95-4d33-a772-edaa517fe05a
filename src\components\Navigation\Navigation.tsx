import React, { useState } from "react";
import type { NavigationProps } from "../../types";
import logo from "../../assets/mkolani-logo.jpg";

const Navigation: React.FC<NavigationProps> = ({
  isMenuOpen,
  setIsMenuOpen,
  scrollToSection,
}) => {
  const [isScrolling, setScrolling] = useState(false);
  const handleScroll = () => {
    setScrolling(window.scrollY > 50);
  }

  window.addEventListener("scroll", handleScroll);
  
  return (
    <nav className={` backdrop-blur-md border-b border-slate-200 fixed w-full top-0 z-50 duration-200 transition-all ${isScrolling ? 'shadow-lg bg-white/75 pt-2' : 'bg-white pt-5'}`}>
      <div className="max-w-full mx-auto md:px-0 px-6">
        <div className="md:flex-row flex-col justify-between items-center h-auto">
          {/* Logo and Institution Name */}
          <div className="flex w-full items-center justify-between space-x-4 md:px-6">
            <div className="flex-shrink-0">
              <img
                src={logo}
                alt="MKOLANI Logo"
                className={` rounded-full ring-2 ring-slate-100 duration-200 aspect-square ${isScrolling ? `w-15` : `w-20`}`}
              />
            </div>
            <div className="hidden sm:block">
              <h1 className={`scroll-m-20 text-center  font-extrabold tracking-[.3rem] text-balance text-slate-700 duration-200 ${isScrolling ? `text-xl` : `text-4xl`}`}>
                MKOLANI FOUNDATION HEALTH SCIENCES TRAINING INSTITUTE
              </h1>
            </div>
            <div className="hidden md:flex flex-shrink-0">
              <img
                src={logo}
                alt="MKOLANI Logo"
                className={` rounded-full ring-2 ring-slate-100 duration-200 aspect-square ${isScrolling ? `w-15` : `w-20`}`}
              />
            </div>
          </div>

          {/* Desktop Navigation */}
          <div className={`hidden md:flex items-center justify-center mt-5 w-full bg-teal-600 ${isScrolling ? `mt-2` : `mt-5`}`}>
            <div className="ml-10 flex items-baseline space-x-1">
              <button
                onClick={() => scrollToSection("home")}
                className=" relative font-heading text-slate-900 hover:text-slate-900  px-4 py-2 text-sm font-bold transition-all duration-200 group cursor-pointer"
              >
                Home
                <span className="absolute bottom-0 right-1/2 w-0 h-1 bg-white group-hover:w-full group-hover:right-0 transition-all duration-200"></span>
              </button>
              <button
                onClick={() => scrollToSection("about")}
                className=" relative font-heading text-slate-900 hover:text-slate-900  px-4 py-2 text-[1rem] font-bold transition-all duration-200 group cursor-pointer"
              >
                About
                <span className="absolute bottom-0 right-1/2 w-0 h-1 bg-white group-hover:w-full group-hover:right-0 transition-all duration-200"></span>
              </button>
              <button
                onClick={() => scrollToSection("programs")}
                className="relative font-heading text-slate-900 hover:text-slate-900  px-4 py-2 text-[1rem] font-bold transition-all duration-200 group cursor-pointer"
              >
                Programs
                <span className="absolute bottom-0 right-1/2 w-0 h-1 bg-white group-hover:w-full group-hover:right-0 transition-all duration-200"></span>
              </button>
              <button
                onClick={() => scrollToSection("gallery")}
                className="relative font-heading text-slate-900 hover:text-slate-900  px-4 py-2 text-[1rem] font-bold transition-all duration-200 rounded-lg group cursor-pointer"
              >
                Gallery
                <span className="absolute bottom-0 right-1/2 w-0 h-1 bg-white group-hover:w-full group-hover:right-0 transition-all duration-200"></span>
              </button>
              <button
                onClick={() => scrollToSection("contact")}
                className="relative font-heading text-slate-900 hover:text-slate-900  px-4 py-2 text-[1rem] font-bold transition-all duration-200 rounded-lg group cursor-pointer"
              >
                Contact
                <span className="absolute bottom-0 right-1/2 w-0 h-1 bg-white group-hover:w-full group-hover:right-0 transition-all duration-200"></span>
              </button>
            </div>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden absolute right-4 top-4">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="text-slate-700 hover:text-slate-900 hover:bg-slate-50 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 p-2 rounded-lg transition-all duration-200"
            >
              <svg
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                {isMenuOpen ? (
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                ) : (
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 6h16M4 12h16M4 18h16"
                  />
                )}
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Navigation Menu */}
        {isMenuOpen && (
          <div className="md:hidden">
            <div className="px-4 pt-2 pb-4 space-y-2 border-t border-slate-200">
              <button
                onClick={() => scrollToSection("home")}
                className="font-heading block text-slate-700 hover:text-slate-900 hover:bg-slate-50 px-4 py-3 text-base font-medium w-full text-left rounded-lg transition-all duration-200"
              >
                Home
              </button>
              <button
                onClick={() => scrollToSection("about")}
                className="font-heading block text-slate-700 hover:text-slate-900 hover:bg-slate-50 px-4 py-3 text-base font-medium w-full text-left rounded-lg transition-all duration-200"
              >
                About
              </button>
              <button
                onClick={() => scrollToSection("programs")}
                className="font-heading block text-slate-700 hover:text-slate-900 hover:bg-slate-50 px-4 py-3 text-base font-medium w-full text-left rounded-lg transition-all duration-200"
              >
                Programs
              </button>
              <button
                onClick={() => scrollToSection("gallery")}
                className="font-heading block text-slate-700 hover:text-slate-900 hover:bg-slate-50 px-4 py-3 text-base font-medium w-full text-left rounded-lg transition-all duration-200"
              >
                Gallery
              </button>
              <button
                onClick={() => scrollToSection("contact")}
                className="font-heading block text-slate-700 hover:text-slate-900 hover:bg-slate-50 px-4 py-3 text-base font-medium w-full text-left rounded-lg transition-all duration-200"
              >
                Contact
              </button>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};

export default Navigation;
