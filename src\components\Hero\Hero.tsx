import React, { useEffect } from "react";
import type { HeroProps } from "../../types";

const Hero: React.FC<HeroProps> = ({
  slides,
  currentSlide,
  setCurrentSlide,
  scrollToSection,
}) => {
  // Auto-advance slideshow
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((currentSlide + 1) % slides.length);
    }, 5000);
    return () => clearInterval(timer);
  }, [currentSlide, slides.length, setCurrentSlide]);

  return (
    <section id="home" className=" relative flex items-start justify-center h-screen overflow-hidden">
      <div className="absolute inset-0">
        {slides.map((slide, index) => (
          <div
            key={index}
            className={`absolute inset-0 transition-opacity duration-1000 ${
              index === currentSlide ? "opacity-100" : "opacity-0"
            }`}
          >
            <img
              src={slide.image}
              alt={slide.title}
              className="w-full h-full object-cover"
            />
            <div className="absolute inset-0 bg-black/50"></div>
          </div>
        ))}
      </div>

      <div className="relative z-10 flex items-center justify-center h-auto mt-10">
        <div className="text-center text-white px-4 max-w-4xl">
          <h1 className="font-heading text-4xl md:text-6xl font-bold mb-6 leading-tight tracking-tight">
            {slides[currentSlide].title}
          </h1>
          <p className="font-body text-xl md:text-2xl mb-10 leading-relaxed text-white/90 font-normal">
            {slides[currentSlide].subtitle}
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center ">
            <button
              onClick={() => scrollToSection("about")}
              className="font-heading bg-white text-slate-900 hover:bg-slate-50 px-8 py-4 rounded-xl text-lg font-semibold transition-all duration-300 shadow-lg hover:shadow-xl"
            >
              Learn More
            </button>
            <button
              onClick={() => scrollToSection("programs")}
              className="font-heading bg-transparent border-2 border-white/80 hover:bg-white hover:text-slate-900 text-white px-8 py-4 rounded-xl text-lg font-semibold transition-all duration-300 backdrop-blur-sm"
            >
              Apply Now
            </button>
          </div>
        </div>
      </div>

      {/* Slide indicators */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-3">
        {slides.map((_, index) => (
          <button
            key={index}
            onClick={() => setCurrentSlide(index)}
            className={`w-3 h-3 rounded-full transition-all duration-300 ${
              index === currentSlide
                ? "bg-white shadow-lg scale-110"
                : "bg-white/50 hover:bg-white/70"
            }`}
          />
        ))}
      </div>
    </section>
  );
};

export default Hero;
