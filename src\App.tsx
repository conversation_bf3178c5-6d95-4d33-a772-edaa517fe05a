import React, { useState, useEffect } from "react";
import {
  Navigation,
  Hero,
  Announcements,
  About,
  Programs,
  Gallery,
  Footer,
} from "./components";
import { slides, announcements, programs, galleryImages } from "./data";

function App() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [currentAnnouncement, setCurrentAnnouncement] = useState(0);

  // Auto-scroll announcements
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentAnnouncement((prev) => (prev + 1) % announcements.length);
    }, 3000);
    return () => clearInterval(timer);
  }, []);

  // Smooth scroll function
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: "smooth" });
    }
    setIsMenuOpen(false);
  };

  return (
    <div className="min-h-screen bg-white">
      <Navigation
        isMenuOpen={isMenuOpen}
        setIsMenuOpen={setIsMenuOpen}
        scrollToSection={scrollToSection}
      />


{/* hero and announcements */}
<section className="relative mt-40">
  <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
    <div className="md:col-span-2">
      <Hero
        slides={slides}
        currentSlide={currentSlide}
        setCurrentSlide={setCurrentSlide}
        scrollToSection={scrollToSection}
      />
      </div>
      <div>
        {/* cannouncements */}
          <div className="bg-white border border-slate-200 p-6 rounded-2xl shadow-sm hover:shadow-md transition-shadow duration-300">
            <div className="flex items-center mb-6">
              <div className="w-12 h-12 bg-slate-100 rounded-full flex items-center justify-center mr-4">
                <svg
                  className="w-6 h-6 text-slate-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"
                  />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-slate-900 tracking-tight">
                Latest Announcements
              </h3>
            </div>
            <div className="h-[400px] overflow-y-auto pr-4 space-y-4">
              <div className="bg-slate-50 p-4 rounded-lg">
                <p className="text-xs text-teal-600 font-semibold mb-2">NEW</p>
                <h4 className="text-slate-900 font-medium mb-2">2024 Admission Open</h4>
                <p className="text-slate-600 text-sm">Applications are now open for the 2024 academic year. Early bird discounts available until March 31st.</p>
                <p className="text-slate-400 text-xs mt-2">Posted: 2 hours ago</p>
              </div>
              <div className="bg-slate-50 p-4 rounded-lg">
                <h4 className="text-slate-900 font-medium mb-2">Research Symposium</h4>
                <p className="text-slate-600 text-sm">Annual Medical Research Symposium scheduled for April 15th. Register now to present your research.</p>
                <p className="text-slate-400 text-xs mt-2">Posted: 1 day ago</p>
              </div>
              <div className="bg-slate-50 p-4 rounded-lg">
                <h4 className="text-slate-900 font-medium mb-2">Scholarship Awards</h4>
                <p className="text-slate-600 text-sm">Merit-based scholarships announced for outstanding students in medical sciences.</p>
                <p className="text-slate-400 text-xs mt-2">Posted: 2 days ago</p>
              </div>
              <div className="bg-slate-50 p-4 rounded-lg">
                <h4 className="text-slate-900 font-medium mb-2">Campus Update</h4>
                <p className="text-slate-600 text-sm">New laboratory equipment installed in the pharmaceutical research wing.</p>
                <p className="text-slate-400 text-xs mt-2">Posted: 3 days ago</p>
              </div>
              <div className="bg-slate-50 p-4 rounded-lg">
                <h4 className="text-slate-900 font-medium mb-2">Guest Lecture Series</h4>
                <p className="text-slate-600 text-sm">Distinguished professors from Johns Hopkins to conduct special sessions next month.</p>
                <p className="text-slate-400 text-xs mt-2">Posted: 4 days ago</p>
              </div>
            </div>
            <div className="mt-4 text-center border-t border-slate-100 pt-4">
              <a href="#" className="text-teal-600 hover:text-teal-700 font-medium text-sm inline-flex items-center">
                See all announcements
                <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </a>
            </div>
          </div>
      </div>
      
      </div>
</section>

      {/* <Announcements
        announcements={announcements}
        currentAnnouncement={currentAnnouncement}
      /> */}
      <About />

      {/* <Programs programs={programs} /> */}

      <Gallery images={galleryImages} />

      <Footer scrollToSection={scrollToSection} />
    </div>
  );
}

export default App;
