import React from "react";
import type { AnnouncementsProps } from "../../types";

const Announcements: React.FC<AnnouncementsProps> = ({
  announcements,
  currentAnnouncement,
}) => {
  return (
    <section className="bg-teal-600 text-white py-4 overflow-hidden">
      <div className="max-w-7xl mx-auto px-4">
        <div className="flex items-center">
          <div className="bg-teal-700 px-4 py-2 rounded-xl mr-4 flex-shrink-0 shadow-sm">
            <span className="font-heading font-semibold text-sm tracking-wide">
              New Announcement
            </span>
          </div>
          <div className="flex-1 overflow-hidden">
            <div className="whitespace-nowrap animate-pulse transition-all duration-500">
              <span className="font-body inline-block mr-16 text-sm font-medium">
                {announcements[currentAnnouncement]}
              </span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Announcements;
